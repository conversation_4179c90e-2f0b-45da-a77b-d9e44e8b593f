import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_zh.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('zh'),
  ];

  /// No description provided for @appTitle.
  ///
  /// In en, this message translates to:
  /// **'ESOP Client'**
  String get appTitle;

  /// No description provided for @settings.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// No description provided for @language.
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// No description provided for @english.
  ///
  /// In en, this message translates to:
  /// **'English'**
  String get english;

  /// No description provided for @chinese.
  ///
  /// In en, this message translates to:
  /// **'Chinese'**
  String get chinese;

  /// No description provided for @settingsSaved.
  ///
  /// In en, this message translates to:
  /// **'Settings saved'**
  String get settingsSaved;

  /// No description provided for @mqttServerAddress.
  ///
  /// In en, this message translates to:
  /// **'Server Address'**
  String get mqttServerAddress;

  /// No description provided for @mqttServerAddressHint.
  ///
  /// In en, this message translates to:
  /// **'e.g., example.com or ip addr'**
  String get mqttServerAddressHint;

  /// No description provided for @pleaseEnterMqttServerAddress.
  ///
  /// In en, this message translates to:
  /// **'Please enter server address'**
  String get pleaseEnterMqttServerAddress;

  /// No description provided for @apiServerPort.
  ///
  /// In en, this message translates to:
  /// **'Api Port'**
  String get apiServerPort;

  /// No description provided for @apiServerPortHint.
  ///
  /// In en, this message translates to:
  /// **'e.g., 8090'**
  String get apiServerPortHint;

  /// No description provided for @mqttPort.
  ///
  /// In en, this message translates to:
  /// **'MQTT Port'**
  String get mqttPort;

  /// No description provided for @mqttPortHint.
  ///
  /// In en, this message translates to:
  /// **'e.g., 1883'**
  String get mqttPortHint;

  /// No description provided for @pleaseEnterMqttPort.
  ///
  /// In en, this message translates to:
  /// **'Please enter MQTT port'**
  String get pleaseEnterMqttPort;

  /// No description provided for @portNumberMustBeBetween.
  ///
  /// In en, this message translates to:
  /// **'Port number must be between 1-65535'**
  String get portNumberMustBeBetween;

  /// No description provided for @pleaseEnterValidPort.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid port number'**
  String get pleaseEnterValidPort;

  /// No description provided for @mqttTopic.
  ///
  /// In en, this message translates to:
  /// **'MQTT Topic'**
  String get mqttTopic;

  /// No description provided for @mqttTopicHint.
  ///
  /// In en, this message translates to:
  /// **'e.g., esopChannel'**
  String get mqttTopicHint;

  /// No description provided for @pleaseEnterMqttTopic.
  ///
  /// In en, this message translates to:
  /// **'Please enter MQTT topic'**
  String get pleaseEnterMqttTopic;

  /// No description provided for @groupName.
  ///
  /// In en, this message translates to:
  /// **'Group Name'**
  String get groupName;

  /// No description provided for @groupNameHint.
  ///
  /// In en, this message translates to:
  /// **'e.g., Production Line 1'**
  String get groupNameHint;

  /// No description provided for @pleaseEnterGroupName.
  ///
  /// In en, this message translates to:
  /// **'Please enter group name'**
  String get pleaseEnterGroupName;

  /// No description provided for @deviceAlias.
  ///
  /// In en, this message translates to:
  /// **'Device Alias'**
  String get deviceAlias;

  /// No description provided for @deviceAliasHint.
  ///
  /// In en, this message translates to:
  /// **'e.g., My Device'**
  String get deviceAliasHint;

  /// No description provided for @pleaseEnterDeviceAlias.
  ///
  /// In en, this message translates to:
  /// **'Please enter device alias'**
  String get pleaseEnterDeviceAlias;

  /// No description provided for @deviceId.
  ///
  /// In en, this message translates to:
  /// **'Device ID'**
  String get deviceId;

  /// No description provided for @loading.
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get loading;

  /// No description provided for @saveSettings.
  ///
  /// In en, this message translates to:
  /// **'Save Settings'**
  String get saveSettings;

  /// No description provided for @connected.
  ///
  /// In en, this message translates to:
  /// **'Connected'**
  String get connected;

  /// No description provided for @disconnected.
  ///
  /// In en, this message translates to:
  /// **'Disconnected'**
  String get disconnected;

  /// No description provided for @mqttStatus.
  ///
  /// In en, this message translates to:
  /// **'MQTT Status: {status}'**
  String mqttStatus(String status);

  /// No description provided for @connect.
  ///
  /// In en, this message translates to:
  /// **'Connect'**
  String get connect;

  /// No description provided for @doubleTapWithTwoFingers.
  ///
  /// In en, this message translates to:
  /// **'Double tap with two fingers to access settings'**
  String get doubleTapWithTwoFingers;

  /// No description provided for @fileOperation.
  ///
  /// In en, this message translates to:
  /// **'File Operation: {status}'**
  String fileOperation(String status);

  /// No description provided for @retry.
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get retry;

  /// No description provided for @usingExistingFile.
  ///
  /// In en, this message translates to:
  /// **'Using existing file'**
  String get usingExistingFile;

  /// No description provided for @checking.
  ///
  /// In en, this message translates to:
  /// **'Checking'**
  String get checking;

  /// No description provided for @openingDocument.
  ///
  /// In en, this message translates to:
  /// **'Opening document...'**
  String get openingDocument;

  /// No description provided for @errorLoadingContent.
  ///
  /// In en, this message translates to:
  /// **'Error loading content: {message}'**
  String errorLoadingContent(String message);

  /// No description provided for @httpError.
  ///
  /// In en, this message translates to:
  /// **'HTTP Error: {statusCode} {description}'**
  String httpError(int statusCode, String description);

  /// No description provided for @error.
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get error;

  /// No description provided for @back.
  ///
  /// In en, this message translates to:
  /// **'Back'**
  String get back;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en', 'zh'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return AppLocalizationsEn();
    case 'zh':
      return AppLocalizationsZh();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
