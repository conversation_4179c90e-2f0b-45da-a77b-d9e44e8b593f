import 'dart:async';
import 'dart:math';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:flutter/foundation.dart';

import '../providers/settings_provider.dart';

class LifeSignalService {
  Timer? _lifeSignalTimer;

  // Start sending life signals every 30-40 seconds random interval
  void startLifeSignal({required SettingsProvider settingsProvider}) {
    // Cancel existing timer if any
    stopLifeSignal();

    // Generate random delay between 30-40 seconds
    final random = Random();
    final duration = Duration(seconds: 30 + random.nextInt(13));

    // Start new timer with random interval
    _lifeSignalTimer = Timer.periodic(
      duration,
      (timer) => sendLifeSignal(settingsProvider: settingsProvider),
    );
  }

  // Stop sending life signals
  void stopLifeSignal() {
    if (_lifeSignalTimer?.isActive == true) {
      _lifeSignalTimer?.cancel();
    }
  }

  // Send life signal to server
  Future<void> sendLifeSignal({
    required SettingsProvider settingsProvider,
  }) async {
    try {
      final settings = settingsProvider.settings;
      final serverAddress = settings.mqttServerAddress ?? '';
      final deviceId = settings.deviceId ?? '';

      // Skip if required parameters are missing
      if (serverAddress.isEmpty || deviceId.isEmpty) {
        debugPrint('Missing required parameters for life signal');
        return;
      }

      // Prepare the API endpoint URL
      String baseUrl = serverAddress;
      String port = settings.serverPort ?? '';

      if (!baseUrl.startsWith('http://') && !baseUrl.startsWith('https://')) {
        baseUrl = 'http://$baseUrl:$port';
      }

      final url = Uri.parse('$baseUrl/v1/equipment/lifeEquipment');

      // Prepare request body with device ID
      final body = {'device_id': deviceId};

      debugPrint('Sending life signal to: $url');
      debugPrint('Request body: $body');

      // Make PUT request
      final response = await http
          .put(
            url,
            headers: {'Content-Type': 'application/json'},
            body: jsonEncode(body),
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        debugPrint('Life signal sent successfully${response.body}');

        try {
          final responseData = jsonDecode(response.body);
          if (responseData['code'] == 0 && responseData['data'] != null) {
            final data = responseData['data'];
            final remoteGroupName = data['group_name']?.toString();
            final remoteAliasName = data['alias_name']?.toString();

            // Compare and update group name if different
            if (remoteGroupName != null &&
                remoteGroupName != settingsProvider.settings.groupName) {
              await settingsProvider.updateGroupName(remoteGroupName);
            }

            // Compare and update alias name if different
            if (remoteAliasName != null &&
                remoteAliasName != settingsProvider.settings.deviceAlias) {
              await settingsProvider.updateDeviceAlias(remoteAliasName);
            }
          }
        } catch (e) {
          debugPrint('Error parsing life signal response: $e');
        }
      } else {
        debugPrint(
          'Failed to send life signal, status: ${response.statusCode}',
        );
      }
    } catch (e, stackTrace) {
      debugPrint('Error sending life signal: $e');
      debugPrint('Stack trace: $stackTrace');
    }
  }
}
