import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';

class DeviceInfoService {
  final DeviceInfoPlugin _deviceInfoPlugin = DeviceInfoPlugin();

  // Get device ID based on platform
  Future<String> getDeviceId() async {
    try {
      if (Platform.isAndroid) {
        return await _getAndroidDeviceId();
      } else if (Platform.isIOS) {
        return await _getIosDeviceId();
      } else if (Platform.isMacOS) {
        return await _getMacOsDeviceId();
      } else if (Platform.isWindows) {
        return await _getWindowsDeviceId();
      } else if (Platform.isLinux) {
        return await _getLinuxDeviceId();
      } else {
        return 'unknown_device';
      }
    } catch (e) {
      print('Error getting device ID: $e');
      return 'error_device_id';
    }
  }

  // Get Android device ID
  Future<String> _getAndroidDeviceId() async {
    final AndroidDeviceInfo androidInfo = await _deviceInfoPlugin.androidInfo;
    return androidInfo.id;
  }

  // Get iOS device ID
  Future<String> _getIosDeviceId() async {
    final IosDeviceInfo iosInfo = await _deviceInfoPlugin.iosInfo;
    return iosInfo.identifierForVendor ?? 'unknown_ios_device';
  }

  // Get macOS device ID
  Future<String> _getMacOsDeviceId() async {
    final MacOsDeviceInfo macOsInfo = await _deviceInfoPlugin.macOsInfo;
    return macOsInfo.systemGUID ?? 'unknown_macos_device';
  }

  // Get Windows device ID
  Future<String> _getWindowsDeviceId() async {
    final WindowsDeviceInfo windowsInfo = await _deviceInfoPlugin.windowsInfo;
    return windowsInfo.deviceId;
  }

  // Get Linux device ID
  Future<String> _getLinuxDeviceId() async {
    final LinuxDeviceInfo linuxInfo = await _deviceInfoPlugin.linuxInfo;
    return linuxInfo.machineId ?? 'unknown_linux_device';
  }
}
