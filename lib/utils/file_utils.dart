import 'package:path/path.dart' as path;

class FileUtils {
  // Supported document file extensions
  static const Set<String> documentExtensions = {
    '.pdf',
    '.doc',
    '.docx',
    '.xls',
    '.xlsx',
    '.ppt',
    '.pptx',
  };

  // ZIP file extensions
  static const Set<String> zipExtensions = {'.zip'};

  /// Check if a file is a document file (PDF, PPT, Excel, etc.)
  static bool isDocumentFile(String filePath) {
    final extension = path.extension(filePath).toLowerCase();
    return documentExtensions.contains(extension);
  }

  /// Check if a file is a PDF file
  static bool isPdfFile(String filePath) {
    final extension = path.extension(filePath).toLowerCase();
    return extension == '.pdf';
  }

  /// Check if a file is a ZIP file
  static bool isZipFile(String filePath) {
    final extension = path.extension(filePath).toLowerCase();
    return zipExtensions.contains(extension);
  }

  /// Get the file extension from a file path
  static String getFileExtension(String filePath) {
    return path.extension(filePath).toLowerCase();
  }

  /// Get the file name without extension
  static String getFileNameWithoutExtension(String filePath) {
    return path.basenameWithoutExtension(filePath);
  }

  /// Get the file name with extension
  static String getFileName(String filePath) {
    return path.basename(filePath);
  }

  /// Check if a file type is supported for opening
  static bool isSupportedFileType(String filePath) {
    return isDocumentFile(filePath) || isZipFile(filePath);
  }

  /// Get MIME type for document files (for Android)
  static String? getMimeType(String filePath) {
    final extension = getFileExtension(filePath);

    switch (extension) {
      case '.pdf':
        return 'application/pdf';
      case '.doc':
        return 'application/msword';
      case '.docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      case '.xls':
        return 'application/vnd.ms-excel';
      case '.xlsx':
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      case '.ppt':
        return 'application/vnd.ms-powerpoint';
      case '.pptx':
        return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
      default:
        return null;
    }
  }
}
