# ESOP Client

一个基于 Flutter 开发的 ESOP（电子标准作业程序）客户端应用，支持通过 MQTT 协议接收文件并在 WebView 中显示内容。

## 项目概述

ESOP Client 是一个跨平台的移动应用，主要用于工业环境中的标准作业程序展示。应用通过 MQTT 协议与服务器通信，接收并展示作业指导文件，支持多语言界面和全屏显示模式。

## 核心功能

### 1. MQTT 通信
- **实时消息接收**：通过 MQTT 协议连接服务器，实时接收作业指导文件
- **自动重连机制**：支持网络断开后的自动重连
- **消息类型支持**：
  - 类型 1：部分发送（针对特定设备）
  - 类型 2：全部发送（广播到所有设备）
  - 类型 3：基于规则的发送（按设备别名匹配）

### 2. 文件管理
- **自动下载**：接收到 MQTT 消息后自动下载指定的 ZIP 文件
- **进度显示**：实时显示文件下载进度
- **智能缓存**：检查本地是否已存在相同文件，避免重复下载
- **自动解压**：下载完成后自动解压 ZIP 文件
- **文件查找**：自动查找解压目录中的 index.html 文件

### 3. WebView 展示
- **全屏显示**：沉浸式全屏模式展示内容
- **本地文件支持**：直接加载本地 HTML 文件
- **JavaScript 支持**：完整的 Web 功能支持
- **导航控制**：支持 WebView 内的前进后退操作

### 4. 设备管理
- **设备注册**：自动向服务器注册设备信息
- **设备识别**：获取唯一设备 ID
- **分组管理**：支持设备分组功能
- **别名设置**：可自定义设备显示名称

### 5. 多语言支持
- **中英文切换**：支持中文和英文界面
- **动态切换**：无需重启应用即可切换语言
- **本地化存储**：语言设置持久化保存

### 6. 设置管理
- **MQTT 配置**：服务器地址、端口、主题配置
- **设备配置**：设备别名、分组名称设置
- **手势访问**：双指双击进入设置界面
- **数据持久化**：所有设置自动保存到本地

## 技术架构

### 架构模式
- **Provider 状态管理**：使用 Provider 模式管理应用状态
- **服务层分离**：业务逻辑与 UI 分离
- **模型驱动**：使用数据模型规范化数据结构

### 核心组件

#### 1. 状态管理 (Providers)
- **MqttProvider**：管理 MQTT 连接状态和消息处理
- **FileProvider**：管理文件下载、解压和状态
- **SettingsProvider**：管理应用设置和配置
- **LocalizationProvider**：管理多语言切换

#### 2. 服务层 (Services)
- **MqttService**：MQTT 客户端封装，处理连接和消息
- **FileService**：文件操作服务，下载、解压、查找
- **ApiService**：HTTP API 调用，设备注册
- **SettingsService**：设置数据持久化
- **DeviceInfoService**：设备信息获取

#### 3. 数据模型 (Models)
- **MqttMessageModel**：MQTT 消息数据结构
- **MqttFileItemModel**：文件项数据结构
- **SettingsModel**：设置数据结构

#### 4. 界面层 (Screens)
- **HomeScreen**：主界面，显示状态和处理消息
- **SettingsScreen**：设置界面，配置应用参数
- **WebViewScreen**：WebView 界面，展示作业内容

### 依赖库

#### 核心功能
- `mqtt_client: ^10.2.0` - MQTT 客户端
- `flutter_inappwebview: ^6.0.0` - WebView 组件
- `provider: ^6.1.1` - 状态管理
- `open_file: ^3.5.10` - 文档文件打开

#### 文件处理
- `dio: ^5.4.1` - HTTP 客户端（支持下载进度）
- `archive: ^3.4.10` - ZIP 文件处理
- `path_provider: ^2.1.2` - 文件路径管理

#### 设备信息
- `device_info_plus: ^9.1.2` - 设备信息获取
- `connectivity_plus: ^5.0.2` - 网络状态检测

#### 数据存储
- `shared_preferences: ^2.2.2` - 本地数据存储

#### 国际化
- `flutter_localizations` - Flutter 国际化支持
- `intl: ^0.20.2` - 国际化工具

## 使用流程

### 1. 首次启动
1. 应用启动后检查必要设置
2. 如设置不完整，自动跳转到设置界面
3. 用户配置服务器地址、分组名称、设备别名等
4. 保存设置后返回主界面

### 2. 设备注册
1. 获取设备唯一 ID
2. 向服务器注册设备信息
3. 包含设备别名、ID、分组、IP 地址等

### 3. MQTT 连接
1. 使用配置的服务器地址和端口连接 MQTT
2. 订阅指定主题（默认：esopChannel）
3. 监听服务器推送的消息

### 4. 文件处理
1. 接收到包含文件列表的 MQTT 消息
2. 根据消息类型和设备匹配规则判断是否处理
3. 下载指定的 ZIP 文件
4. 自动解压并查找 index.html
5. 在 WebView 中打开文件

### 5. 内容展示
1. 全屏模式显示作业指导内容
2. 支持 Web 页面的所有交互功能
3. 支持页面内导航（前进/后退）

## 配置说明

### MQTT 配置
- **服务器地址**：MQTT 服务器的 IP 地址或域名
- **端口**：MQTT 服务器端口（默认：1883）
- **主题**：订阅的 MQTT 主题（默认：esopChannel）

### 设备配置
- **设备别名**：设备的显示名称，用于服务器识别
- **分组名称**：设备所属的分组，用于消息过滤
- **设备 ID**：系统自动生成的唯一标识符

### 语言配置
- **中文**：简体中文界面
- **英文**：英文界面

## 开发环境

### 系统要求
- Flutter SDK 3.8.0+
- Dart 3.0+
- Android Studio / VS Code
- iOS 开发需要 Xcode

### 平台支持
- ✅ Android
- ✅ iOS
- ✅ Windows
- ✅ macOS
- ✅ Linux
- ✅ Web

## 安装和运行

### 1. 克隆项目
```bash
git clone <repository-url>
cd esop_client
```

### 2. 安装依赖
```bash
flutter pub get
```

### 3. 生成国际化文件
```bash
flutter gen-l10n
```

### 4. 运行应用
```bash
# 调试模式
flutter run

# 发布模式
flutter run --release
```

### 5. 构建发布版本
```bash
# Android APK
flutter build apk --release

# iOS
flutter build ios --release

# Windows
flutter build windows --release
```

## 项目结构

```
lib/
├── l10n/                    # 国际化文件
│   ├── app_en.arb          # 英文资源
│   ├── app_zh.arb          # 中文资源
│   └── ...
├── models/                  # 数据模型
│   ├── mqtt_message_model.dart
│   ├── mqtt_file_item_model.dart
│   └── settings_model.dart
├── providers/               # 状态管理
│   ├── mqtt_provider.dart
│   ├── file_provider.dart
│   ├── settings_provider.dart
│   └── localization_provider.dart
├── screens/                 # 界面
│   ├── home_screen.dart
│   ├── settings_screen.dart
│   └── webview_screen.dart
├── services/                # 服务层
│   ├── mqtt_service.dart
│   ├── file_service.dart
│   ├── api_service.dart
│   ├── settings_service.dart
│   └── device_info_service.dart
├── utils/                   # 工具类
│   └── gesture_detector.dart
├── widgets/                 # 自定义组件
└── main.dart               # 应用入口
```

## 特性说明

### 全屏模式
应用采用沉浸式全屏模式，隐藏状态栏和导航栏，提供更好的内容展示体验。

### 手势控制
- **双指双击**：在主界面双指双击可进入设置界面
- **返回控制**：在 WebView 中支持返回到上一页或退出

### 智能文件管理
- **缓存机制**：存储30天内的下载文件及压缩包，避免重复下载相同文件
- **自动清理**：管理临时文件和缓存，自动清理30天后的无用文件及压缩包
- **错误处理**：网络异常和文件损坏的处理

### 网络适应性
- **连接检测**：自动检测网络连接状态
- **重连机制**：网络恢复后自动重连 MQTT
- **超时处理**：网络请求超时保护

## 故障排除

### 常见问题

1. **MQTT 连接失败**
   - 检查服务器地址和端口配置
   - 确认网络连接正常
   - 检查防火墙设置

2. **文件下载失败**
   - 检查网络连接
   - 确认文件 URL 有效
   - 检查存储空间

3. **WebView 显示异常**
   - 确认 HTML 文件完整
   - 检查文件路径正确
   - 清除应用缓存

### 调试模式
应用包含详细的调试日志，可通过 Flutter 调试工具查看运行状态和错误信息。

MQTT数据格式：
```json
{
  "type": 3, // 消息类型. 1:部份发送 2:全部发送 3:按规则发送
  "group_name": "scxa", // 组名
  "list": [
    {
      "group_name": "scxa", // 组名
      "download_file": "assets/zippack/55454.zip", // 下载文件
      "file_type": "html", // 文件类型
      "equipment_alias_name": "1,2,3,4,5" // 设备别名
    },
    {
      "group_name": "scxa", // 组名
      "download_file": "assets/zippack/55454.zip", // 下载文件
      "file_type": "html", // 文件类型
      "equipment_alias_name": "1,2,3,4,5" // 设备别名
    }
  ]
}
```